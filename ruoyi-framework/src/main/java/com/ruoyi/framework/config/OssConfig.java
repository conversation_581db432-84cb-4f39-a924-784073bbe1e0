package com.ruoyi.framework.config;

import com.ruoyi.common.utils.OssUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "aliyun.oss", name = "endpoint")
public class OssConfig {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;
    @Value("${aliyun.oss.accessKey}")
    private String accessKeyId;
    @Value("${aliyun.oss.secretKey}")
    private String accessKeySecret;
    @Value("${aliyun.oss.bucketName}")
    private String bucketName;
    @Value("${aliyun.oss.staticDomain:}")
    private String staticDomain;


    @Bean
    public void initOssBootConfiguration() {
        OssUtil.setEndPoint(endpoint);
        OssUtil.setAccessKeyId(accessKeyId);
        OssUtil.setAccessKeySecret(accessKeySecret);
        OssUtil.setBucketName(bucketName);
        OssUtil.setStaticDomain(staticDomain);
    }
}
