package com.ruoyi.yanbao.entity.vo;

import com.ruoyi.common.utils.OssUtil;
import com.ruoyi.yanbao.entity.ProductOrder;

import java.util.List;

/**
 * 订单详情VO类
 * 
 * <AUTHOR>
 */
public class ProductOrderDetailVo extends ProductOrder {
    
    /** 证件图片列表 */
    private List<OssUtil.FileItem> certificateImgList;
    
    /** 行驶证图片列表 */
    private List<OssUtil.FileItem> vehicleLicenseImgList;
    
    /** 购车发票图片列表 */
    private List<OssUtil.FileItem> carInvoiceImgList;
    
    /** 左前45°图片列表 */
    private List<OssUtil.FileItem> leftImgList;
    
    /** 右前45°图片列表 */
    private List<OssUtil.FileItem> rightImgList;
    
    /** 左后45°图片列表 */
    private List<OssUtil.FileItem> leftbackImgList;
    
    /** 右后45°图片列表 */
    private List<OssUtil.FileItem> rightbackImgList;
    
    /** VIN码图片列表 */
    private List<OssUtil.FileItem> vinImgList;
    
    /** 行驶里程图片列表 */
    private List<OssUtil.FileItem> carMileageImgList;
    
    /** 完税证明图片列表 */
    private List<OssUtil.FileItem> purchaseTaxCompleteImgList;
    
    /** 上牌费用发票图片列表 */
    private List<OssUtil.FileItem> vehicleLicenseInvoiceImgList;
    
    /** 交强险保单图片列表 */
    private List<OssUtil.FileItem> trafficInsuranceImgList;
    
    /** 付款小票图片列表 */
    private List<OssUtil.FileItem> payImgList;

    public List<OssUtil.FileItem> getCertificateImgList() {
        return certificateImgList;
    }

    public void setCertificateImgList(List<OssUtil.FileItem> certificateImgList) {
        this.certificateImgList = certificateImgList;
    }

    public List<OssUtil.FileItem> getVehicleLicenseImgList() {
        return vehicleLicenseImgList;
    }

    public void setVehicleLicenseImgList(List<OssUtil.FileItem> vehicleLicenseImgList) {
        this.vehicleLicenseImgList = vehicleLicenseImgList;
    }

    public List<OssUtil.FileItem> getCarInvoiceImgList() {
        return carInvoiceImgList;
    }

    public void setCarInvoiceImgList(List<OssUtil.FileItem> carInvoiceImgList) {
        this.carInvoiceImgList = carInvoiceImgList;
    }

    public List<OssUtil.FileItem> getLeftImgList() {
        return leftImgList;
    }

    public void setLeftImgList(List<OssUtil.FileItem> leftImgList) {
        this.leftImgList = leftImgList;
    }

    public List<OssUtil.FileItem> getRightImgList() {
        return rightImgList;
    }

    public void setRightImgList(List<OssUtil.FileItem> rightImgList) {
        this.rightImgList = rightImgList;
    }

    public List<OssUtil.FileItem> getLeftbackImgList() {
        return leftbackImgList;
    }

    public void setLeftbackImgList(List<OssUtil.FileItem> leftbackImgList) {
        this.leftbackImgList = leftbackImgList;
    }

    public List<OssUtil.FileItem> getRightbackImgList() {
        return rightbackImgList;
    }

    public void setRightbackImgList(List<OssUtil.FileItem> rightbackImgList) {
        this.rightbackImgList = rightbackImgList;
    }

    public List<OssUtil.FileItem> getVinImgList() {
        return vinImgList;
    }

    public void setVinImgList(List<OssUtil.FileItem> vinImgList) {
        this.vinImgList = vinImgList;
    }

    public List<OssUtil.FileItem> getCarMileageImgList() {
        return carMileageImgList;
    }

    public void setCarMileageImgList(List<OssUtil.FileItem> carMileageImgList) {
        this.carMileageImgList = carMileageImgList;
    }

    public List<OssUtil.FileItem> getPurchaseTaxCompleteImgList() {
        return purchaseTaxCompleteImgList;
    }

    public void setPurchaseTaxCompleteImgList(List<OssUtil.FileItem> purchaseTaxCompleteImgList) {
        this.purchaseTaxCompleteImgList = purchaseTaxCompleteImgList;
    }

    public List<OssUtil.FileItem> getVehicleLicenseInvoiceImgList() {
        return vehicleLicenseInvoiceImgList;
    }

    public void setVehicleLicenseInvoiceImgList(List<OssUtil.FileItem> vehicleLicenseInvoiceImgList) {
        this.vehicleLicenseInvoiceImgList = vehicleLicenseInvoiceImgList;
    }

    public List<OssUtil.FileItem> getTrafficInsuranceImgList() {
        return trafficInsuranceImgList;
    }

    public void setTrafficInsuranceImgList(List<OssUtil.FileItem> trafficInsuranceImgList) {
        this.trafficInsuranceImgList = trafficInsuranceImgList;
    }

    public List<OssUtil.FileItem> getPayImgList() {
        return payImgList;
    }

    public void setPayImgList(List<OssUtil.FileItem> payImgList) {
        this.payImgList = payImgList;
    }
}
