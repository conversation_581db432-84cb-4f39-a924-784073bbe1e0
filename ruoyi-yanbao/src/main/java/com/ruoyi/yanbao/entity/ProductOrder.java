package com.ruoyi.yanbao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@TableName("p_product_order")
public class ProductOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1：已删除，0：未删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDelete;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 修改人
     */
    private String changedBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date changedAt;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 业务员id
     */
    private Long saleUserId;

    /**
     * 业务员姓名
     */
    private String saleUserName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 保障期限ID
     */
    private Long productTermId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 保障期限名称
     */
    private String productTermName;

    /**
     * 证件类型：1：身份证，2：营业执照
     */
    private Integer certificateType;

    /**
     * 证件图片
     */
    private Long certificateImg;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 证件号
     */
    private String certificateNo;

    /**
     * 联系电话
     */
    private String contactTelephone;

    /**
     * 行驶证图片
     */
    private Long vehicleLicenseImg;

    /**
     * 车架号
     */
    private String vinNo;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 注册日期
     */
    private Date registrationDate;

    /**
     * 发证日期
     */
    private Date issueDate;

    /**
     * 车辆使用性质：1：非营运，2：营运
     */
    private Integer carUseage;

    /**
     * 车辆品牌ID
     */
    private Long carBrandId;

    /**
     * 车系ID
     */
    private Long carSeriesId;

    /**
     * 车辆型号
     */
    private String carSeriesName;

    /**
     * 动力类型：1：燃油（油混）汽车，2：纯电动汽车，3：插电混合动力汽车，4：增程式电动汽车
     */
    private Integer powerType;

    /**
     * 购车发票
     */
    private Long carInvoiceImg;

    /**
     * 购车金额
     */
    private BigDecimal carPrice;

    /**
     * 购车日期
     */
    private Date carBuyDate;

    /**
     * 左前45°图片
     */
    private Long leftImg;

    /**
     * 右前45°图片
     */
    private Long rightImg;

    /**
     * 左后45°图片
     */
    private Long leftbackImg;

    /**
     * 右后45°图片
     */
    private Long rightbackImg;

    /**
     * 车架号图片
     */
    private Long vinImg;

    /**
     * 行驶里程图片
     */
    private Long carMileageImg;

    /**
     * 行驶里程
     */
    private Long carMileage;

    /**
     * 车船税金额
     */
    private BigDecimal vehicleTaxPrice;

    /**
     * 购置税金额
     */
    private BigDecimal purchaseTaxPrice;

    /**
     * 完税证明图片
     */
    private Long purchaseTaxCompleteImg;

    /**
     * 车辆上牌费用
     */
    private BigDecimal vehicleLicensePrice;

    /**
     * 上牌费用发票
     */
    private Long vehicleLicenseInvoiceImg;

    /**
     * 交强险保单
     */
    private Long trafficInsuranceImg;

    /**
     * 服务生效日期
     */
    private Date serviceEnableDate;

    /**
     * 服务期限（月）
     */
    private Integer servicePeriod;

    /**
     * 经销商ID
     */
    private Long storeId;

    /**
     * 经销商名称
     */
    private String storeName;

    /**
     * 建议售价
     */
    private BigDecimal suggestPrice;

    /**
     * 支付方式：1：线下收款，2：分期支付
     */
    private Integer payType;

    /**
     * 付款小票单号
     */
    private String payNo;

    /**
     * 付款金额
     */
    private BigDecimal payPrice;

    /**
     * 付款小票图片
     */
    private Long payImg;

    /**
     * 分期数
     */
    private Integer payPeriod;

    /**
     * 首期时间
     */
    private Date payFirstDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提交状态：0：暂存，1：已提交
     */
    private Integer submitState;

    /**
     * 审核状态：0：未审核，1：审核通过，2：驳回
     */
    private Integer auditState;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }

    public Date getChangedAt() {
        return changedAt;
    }

    public void setChangedAt(Date changedAt) {
        this.changedAt = changedAt;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getSaleUserId() {
        return saleUserId;
    }

    public void setSaleUserId(Long saleUserId) {
        this.saleUserId = saleUserId;
    }

    public String getSaleUserName() {
        return saleUserName;
    }

    public void setSaleUserName(String saleUserName) {
        this.saleUserName = saleUserName;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductTermId() {
        return productTermId;
    }

    public void setProductTermId(Long productTermId) {
        this.productTermId = productTermId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductTermName() {
        return productTermName;
    }

    public void setProductTermName(String productTermName) {
        this.productTermName = productTermName;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public Long getCertificateImg() {
        return certificateImg;
    }

    public void setCertificateImg(Long certificateImg) {
        this.certificateImg = certificateImg;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getContactTelephone() {
        return contactTelephone;
    }

    public void setContactTelephone(String contactTelephone) {
        this.contactTelephone = contactTelephone;
    }

    public Long getVehicleLicenseImg() {
        return vehicleLicenseImg;
    }

    public void setVehicleLicenseImg(Long vehicleLicenseImg) {
        this.vehicleLicenseImg = vehicleLicenseImg;
    }

    public String getVinNo() {
        return vinNo;
    }

    public void setVinNo(String vinNo) {
        this.vinNo = vinNo;
    }

    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public Integer getCarUseage() {
        return carUseage;
    }

    public void setCarUseage(Integer carUseage) {
        this.carUseage = carUseage;
    }

    public Long getCarBrandId() {
        return carBrandId;
    }

    public void setCarBrandId(Long carBrandId) {
        this.carBrandId = carBrandId;
    }

    public Long getCarSeriesId() {
        return carSeriesId;
    }

    public void setCarSeriesId(Long carSeriesId) {
        this.carSeriesId = carSeriesId;
    }

    public String getCarSeriesName() {
        return carSeriesName;
    }

    public void setCarSeriesName(String carSeriesName) {
        this.carSeriesName = carSeriesName;
    }

    public Integer getPowerType() {
        return powerType;
    }

    public void setPowerType(Integer powerType) {
        this.powerType = powerType;
    }

    public Long getCarInvoiceImg() {
        return carInvoiceImg;
    }

    public void setCarInvoiceImg(Long carInvoiceImg) {
        this.carInvoiceImg = carInvoiceImg;
    }

    public BigDecimal getCarPrice() {
        return carPrice;
    }

    public void setCarPrice(BigDecimal carPrice) {
        this.carPrice = carPrice;
    }

    public Date getCarBuyDate() {
        return carBuyDate;
    }

    public void setCarBuyDate(Date carBuyDate) {
        this.carBuyDate = carBuyDate;
    }

    public Long getLeftImg() {
        return leftImg;
    }

    public void setLeftImg(Long leftImg) {
        this.leftImg = leftImg;
    }

    public Long getRightImg() {
        return rightImg;
    }

    public void setRightImg(Long rightImg) {
        this.rightImg = rightImg;
    }

    public Long getLeftbackImg() {
        return leftbackImg;
    }

    public void setLeftbackImg(Long leftbackImg) {
        this.leftbackImg = leftbackImg;
    }

    public Long getRightbackImg() {
        return rightbackImg;
    }

    public void setRightbackImg(Long rightbackImg) {
        this.rightbackImg = rightbackImg;
    }

    public Long getVinImg() {
        return vinImg;
    }

    public void setVinImg(Long vinImg) {
        this.vinImg = vinImg;
    }

    public Long getCarMileageImg() {
        return carMileageImg;
    }

    public void setCarMileageImg(Long carMileageImg) {
        this.carMileageImg = carMileageImg;
    }

    public Long getCarMileage() {
        return carMileage;
    }

    public void setCarMileage(Long carMileage) {
        this.carMileage = carMileage;
    }

    public BigDecimal getVehicleTaxPrice() {
        return vehicleTaxPrice;
    }

    public void setVehicleTaxPrice(BigDecimal vehicleTaxPrice) {
        this.vehicleTaxPrice = vehicleTaxPrice;
    }

    public BigDecimal getPurchaseTaxPrice() {
        return purchaseTaxPrice;
    }

    public void setPurchaseTaxPrice(BigDecimal purchaseTaxPrice) {
        this.purchaseTaxPrice = purchaseTaxPrice;
    }

    public Long getPurchaseTaxCompleteImg() {
        return purchaseTaxCompleteImg;
    }

    public void setPurchaseTaxCompleteImg(Long purchaseTaxCompleteImg) {
        this.purchaseTaxCompleteImg = purchaseTaxCompleteImg;
    }

    public BigDecimal getVehicleLicensePrice() {
        return vehicleLicensePrice;
    }

    public void setVehicleLicensePrice(BigDecimal vehicleLicensePrice) {
        this.vehicleLicensePrice = vehicleLicensePrice;
    }

    public Long getVehicleLicenseInvoiceImg() {
        return vehicleLicenseInvoiceImg;
    }

    public void setVehicleLicenseInvoiceImg(Long vehicleLicenseInvoiceImg) {
        this.vehicleLicenseInvoiceImg = vehicleLicenseInvoiceImg;
    }

    public Long getTrafficInsuranceImg() {
        return trafficInsuranceImg;
    }

    public void setTrafficInsuranceImg(Long trafficInsuranceImg) {
        this.trafficInsuranceImg = trafficInsuranceImg;
    }

    public Date getServiceEnableDate() {
        return serviceEnableDate;
    }

    public void setServiceEnableDate(Date serviceEnableDate) {
        this.serviceEnableDate = serviceEnableDate;
    }

    public Integer getServicePeriod() {
        return servicePeriod;
    }

    public void setServicePeriod(Integer servicePeriod) {
        this.servicePeriod = servicePeriod;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public BigDecimal getSuggestPrice() {
        return suggestPrice;
    }

    public void setSuggestPrice(BigDecimal suggestPrice) {
        this.suggestPrice = suggestPrice;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public BigDecimal getPayPrice() {
        return payPrice;
    }

    public void setPayPrice(BigDecimal payPrice) {
        this.payPrice = payPrice;
    }

    public Long getPayImg() {
        return payImg;
    }

    public void setPayImg(Long payImg) {
        this.payImg = payImg;
    }

    public Integer getPayPeriod() {
        return payPeriod;
    }

    public void setPayPeriod(Integer payPeriod) {
        this.payPeriod = payPeriod;
    }

    public Date getPayFirstDate() {
        return payFirstDate;
    }

    public void setPayFirstDate(Date payFirstDate) {
        this.payFirstDate = payFirstDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSubmitState() {
        return submitState;
    }

    public void setSubmitState(Integer submitState) {
        this.submitState = submitState;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    @Override
    public String toString() {
        return "ProductOrder{" +
            "id = " + id +
            ", isDelete = " + isDelete +
            ", createdBy = " + createdBy +
            ", createdAt = " + createdAt +
            ", changedBy = " + changedBy +
            ", changedAt = " + changedAt +
            ", orderNo = " + orderNo +
            ", saleUserId = " + saleUserId +
            ", saleUserName = " + saleUserName +
            ", productId = " + productId +
            ", productTermId = " + productTermId +
            ", productName = " + productName +
            ", productTermName = " + productTermName +
            ", certificateType = " + certificateType +
            ", certificateImg = " + certificateImg +
            ", customerName = " + customerName +
            ", certificateNo = " + certificateNo +
            ", contactTelephone = " + contactTelephone +
            ", vehicleLicenseImg = " + vehicleLicenseImg +
            ", vinNo = " + vinNo +
            ", engineNo = " + engineNo +
            ", carNo = " + carNo +
            ", registrationDate = " + registrationDate +
            ", issueDate = " + issueDate +
            ", carUseage = " + carUseage +
            ", carBrandId = " + carBrandId +
            ", carSeriesId = " + carSeriesId +
            ", carSeriesName = " + carSeriesName +
            ", powerType = " + powerType +
            ", carInvoiceImg = " + carInvoiceImg +
            ", carPrice = " + carPrice +
            ", carBuyDate = " + carBuyDate +
            ", leftImg = " + leftImg +
            ", rightImg = " + rightImg +
            ", leftbackImg = " + leftbackImg +
            ", rightbackImg = " + rightbackImg +
            ", vinImg = " + vinImg +
            ", carMileageImg = " + carMileageImg +
            ", carMileage = " + carMileage +
            ", vehicleTaxPrice = " + vehicleTaxPrice +
            ", purchaseTaxPrice = " + purchaseTaxPrice +
            ", purchaseTaxCompleteImg = " + purchaseTaxCompleteImg +
            ", vehicleLicensePrice = " + vehicleLicensePrice +
            ", vehicleLicenseInvoiceImg = " + vehicleLicenseInvoiceImg +
            ", trafficInsuranceImg = " + trafficInsuranceImg +
            ", serviceEnableDate = " + serviceEnableDate +
            ", servicePeriod = " + servicePeriod +
            ", storeId = " + storeId +
            ", storeName = " + storeName +
            ", suggestPrice = " + suggestPrice +
            ", payType = " + payType +
            ", payNo = " + payNo +
            ", payPrice = " + payPrice +
            ", payImg = " + payImg +
            ", payPeriod = " + payPeriod +
            ", payFirstDate = " + payFirstDate +
            ", remark = " + remark +
            ", submitState = " + submitState +
            ", auditState = " + auditState +
            "}";
    }
}
