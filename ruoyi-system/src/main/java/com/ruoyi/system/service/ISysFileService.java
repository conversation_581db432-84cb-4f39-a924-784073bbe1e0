package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysFile;

/**
 * 文件管理Service接口
 * 
 * <AUTHOR>
 */
public interface ISysFileService 
{
    /**
     * 查询文件信息
     * 
     * @param id 文件主键
     * @return 文件信息
     */
    public SysFile selectSysFileById(Long id);

    /**
     * 查询文件信息列表
     * 
     * @param sysFile 文件信息
     * @return 文件信息集合
     */
    public List<SysFile> selectSysFileList(SysFile sysFile);

    /**
     * 新增文件信息
     * 
     * @param sysFile 文件信息
     * @return 结果
     */
    public int insertSysFile(SysFile sysFile);

    /**
     * 修改文件信息
     * 
     * @param sysFile 文件信息
     * @return 结果
     */
    public int updateSysFile(SysFile sysFile);

    /**
     * 批量删除文件信息
     * 
     * @param ids 需要删除的文件主键集合
     * @return 结果
     */
    public int deleteSysFileByIds(Long[] ids);

    /**
     * 删除文件信息
     * 
     * @param id 文件主键
     * @return 结果
     */
    public int deleteSysFileById(Long id);

    /**
     * 根据关联表和关联ID查询文件列表
     * 
     * @param relatedTable 关联表名
     * @param relatedId 关联表ID
     * @return 文件信息集合
     */
    public List<SysFile> selectSysFileByRelated(String relatedTable, Long relatedId);

    /**
     * 根据存储路径查询文件信息
     *
     * @param path 存储路径
     * @return 文件信息
     */
    public SysFile selectSysFileByPath(String path);

    /**
     * 根据文件ID列表查询文件信息列表
     *
     * @param ids 文件ID列表
     * @return 文件信息集合
     */
    public List<SysFile> selectSysFileByIds(Long[] ids);

    /**
     * 根据文件ID列表查询文件信息列表
     *
     * @param ids 文件ID列表
     * @return 文件信息集合
     */
    public List<SysFile> selectSysFileByIds(Long[] ids);
}
