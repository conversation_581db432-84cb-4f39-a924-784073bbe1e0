<template>
  <div>
    <el-form :model="form" label-width="120px">
      <el-card>
        <div slot="header" class="clearfix">
          <span>车主信息</span>
        </div>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item label="订单编号">
              <el-form-item label="证件类型">
                <span>{{ getCertificateTypeText(form.certificateType) }}</span>
              </el-form-item>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车主姓名">
              <span>{{ form.customerName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="证件号">
              <span>{{ form.certificateNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话">
              <span>{{ form.contactTelephone }}</span>
            </el-form-item>
          </el-col>
        </el-row>




      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header" class="clearfix">
          <span>行驶本信息</span>
        </div>
        <el-divider></el-divider>
        <el-form-item label="车架号">
          <span>{{ form.vinNo }}</span>
        </el-form-item>
        <el-form-item label="发动机号">
          <span>{{ form.engineNo }}</span>
        </el-form-item>
        <el-form-item label="车牌号码">
          <span>{{ form.carNo }}</span>
        </el-form-item>
        <el-form-item label="注册日期">
          <span>{{ parseTime(form.registrationDate, '{y}-{m}-{d}') }}</span>
        </el-form-item>
        <el-form-item label="发证日期">
          <span>{{ parseTime(form.issueDate, '{y}-{m}-{d}') }}</span>
        </el-form-item>
        <el-form-item label="使用性质">
          <span>{{ getCarUsageText(form.carUseage) }}</span>
        </el-form-item>
        <el-form-item label="车辆品牌">
          <span>{{ getCarBrandText(form.carBrandId) }}</span>
        </el-form-item>
        <el-form-item label="车辆型号">
          <span>{{ form.carSeriesName }}</span>
        </el-form-item>
        <el-form-item label="动力类型">
          <span>{{ getPowerTypeText(form.powerType) }}</span>
        </el-form-item>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header" class="clearfix">
          <span>购车发票</span>
        </div>
        <el-divider></el-divider>
        <el-form-item label="购车发票">
          <ImageUpload :disabled="true" v-model="form.carInvoiceImgList" :isShowTip="false"></ImageUpload>
        </el-form-item>
        <el-form-item label="购车金额">
          <span>{{ formatMoney(form.carPrice) }}</span>
        </el-form-item>
        <el-form-item label="购车日期">
          <span>{{ parseTime(form.carBuyDate, '{y}-{m}-{d}') }}</span>
        </el-form-item>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header" class="clearfix">
          <span>汽车图片</span>
        </div>
        <el-divider></el-divider>
        <el-form-item label="车辆图片">
          <el-row>
            <el-col :span="12">
              <div class="image-label">左前45°</div>
              <ImageUpload :disabled="true" cover="/images/left45.jpg" v-model="form.leftImgList" :isShowTip="false"></ImageUpload>
            </el-col>
            <el-col :span="12">
              <div class="image-label">右前45°</div>
              <ImageUpload :disabled="true" cover="/images/right45.png" v-model="form.rightImgList" :isShowTip="false"></ImageUpload>
            </el-col>
            <el-col :span="12">
              <div class="image-label">左后45°</div>
              <ImageUpload :disabled="true" cover="/images/leftback45.png" v-model="form.leftbackImgList" :isShowTip="false"></ImageUpload>
            </el-col>
            <el-col :span="12">
              <div class="image-label">右后45°</div>
              <ImageUpload :disabled="true" cover="/images/rightback45.png" v-model="form.rightbackImgList" :isShowTip="false"></ImageUpload>
            </el-col>
            <el-col :span="12">
              <div class="image-label">VIN码</div>
              <ImageUpload :disabled="true" cover="/images/vin.png" v-model="form.vinImgList" :isShowTip="false"></ImageUpload>
            </el-col>
          </el-row>
        </el-form-item>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header" class="clearfix">
          <span>车辆行驶里程</span>
        </div>
        <el-divider></el-divider>
        <el-form-item label="里程图片">
          <ImageUpload :disabled="true" v-model="form.carMileageImgList" :isShowTip="false"></ImageUpload>
        </el-form-item>
        <el-form-item label="行驶里程">
          <span>{{ form.carMileage }} 公里</span>
        </el-form-item>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header" class="clearfix">
          <span>购车税费</span>
        </div>
        <el-divider></el-divider>
        <el-form-item label="车船税金额">
          <span>{{ formatMoney(form.vehicleTaxPrice) }}</span>
        </el-form-item>
        <el-form-item label="购置税金额">
          <span>{{ formatMoney(form.purchaseTaxPrice) }}</span>
        </el-form-item>
        <el-form-item label="完税证明">
          <ImageUpload :disabled="true" v-model="form.purchaseTaxCompleteImgList" :isShowTip="false"></ImageUpload>
        </el-form-item>
        <el-form-item label="车辆上牌费用">
          <span>{{ formatMoney(form.vehicleLicensePrice) }}</span>
        </el-form-item>
        <el-form-item label="上牌费用发票">
          <ImageUpload :disabled="true" v-model="form.vehicleLicenseInvoiceImgList" :isShowTip="false"></ImageUpload>
        </el-form-item>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header" class="clearfix">
          <span>交强险保单</span>
        </div>
        <el-divider></el-divider>
        <el-form-item label="保单图片">
          <ImageUpload :disabled="true" v-model="form.trafficInsuranceImgList" :isShowTip="false"></ImageUpload>
        </el-form-item>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header" class="clearfix">
          <span>服务信息</span>
        </div>
        <el-divider></el-divider>
        <el-form-item label="服务生效日期">
          <span>{{ parseTime(form.serviceEnableDate, '{y}-{m}-{d}') }}</span>
        </el-form-item>
        <el-form-item label="服务期限">
          <span>{{ getProductTermText(form.productTermId) }}</span>
        </el-form-item>
        <el-form-item label="经销商名称">
          <span>{{ getStoreText(form.storeId) }}</span>
        </el-form-item>
        <el-form-item label="付款小票单号">
          <span>{{ form.payNo }}</span>
        </el-form-item>
        <el-form-item label="付款小票金额">
          <span>{{ formatMoney(form.payPrice) }}</span>
        </el-form-item>
        <el-form-item label="付款小票图片">
          <ImageUpload :disabled="true" v-model="form.payImgList" :isShowTip="false"></ImageUpload>
        </el-form-item>
        <el-divider></el-divider>
        <el-form-item label="备注">
          <span>{{ form.remark || '无' }}</span>
        </el-form-item>
      </el-card>
    </el-form>
    <div style="padding: 10px">
      <el-button @click="goBack" type="primary" style="width: 100%;">返回</el-button>
    </div>
    <div style="height: 50px"></div>
  </div>
</template>

<style scoped>
  .image-label {
    text-align: center;
    margin-bottom: 5px;
    font-size: 12px;
    color: #666;
  }
  :deep(.el-upload--picture-card) {
    --el-upload-picture-card-size:100px !important;
  }
  :deep(.el-upload-list--picture-card) {
    --el-upload-list-picture-card-size: 100px !important;
  }
  :deep(.avatar) {
    width: 100px;
    height: 100px;
    display: block;
  }
</style>

<script setup name="OrderDetail">
import { getOrder } from "@/api/yanbao/productOrder"
import { getProductInfo, getProductSuitStore } from "@/api/yanbao/product"
import { parseTime } from '@/utils/ruoyi'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const data = reactive({
  form: {},
  productTerms: [],
  stores: []
})

const { form, productTerms, stores } = toRefs(data)

// 证件类型转换
function getCertificateTypeText(value) {
  const types = {
    '1': '身份证',
    '2': '营业执照'
  }
  return types[value] || value
}

// 使用性质转换
function getCarUsageText(value) {
  const usages = {
    '1': '非营运',
    '2': '营运'
  }
  return usages[value] || value
}

// 车辆品牌转换
function getCarBrandText(value) {
  const brands = {
    '1': '奥迪',
    '2': '宝马'
  }
  return brands[value] || value
}

// 动力类型转换
function getPowerTypeText(value) {
  const types = {
    '1': '燃油（油混）汽车',
    '2': '纯电动汽车',
    '3': '插电混合动力汽车',
    '4': '增程式电动汽车'
  }
  return types[value] || value
}

// 服务期限转换
function getProductTermText(value) {
  const term = productTerms.value.find(item => item.id === value)
  return term ? term.name : value
}

// 经销商转换
function getStoreText(value) {
  const store = stores.value.find(item => item.id === value)
  return store ? store.name : value
}

// 金额格式化
function formatMoney(value) {
  if (value === null || value === undefined || value === '') {
    return '0.00元'
  }
  return parseFloat(value).toFixed(2) + '元'
}

// 返回上一页
function goBack() {
  router.back()
}

// 获取订单详情
function getOrderDetail() {
  const orderId = route.params.orderId || route.query.orderId
  if (!orderId) {
    proxy.$modal.msgError("订单ID不能为空")
    return
  }

  getOrder({orderId: orderId}).then(response => {
    form.value = response.data
  }).catch(() => {
    proxy.$modal.msgError("获取订单详情失败")
  })
}

// 获取产品信息和经销商信息
function getProductData() {
  const productId = form.value.productId
  if (productId) {
    getProductInfo({ productId }).then(resp => {
      productTerms.value = resp.data.terms
    })
    getProductSuitStore({ productId }).then(resp => {
      stores.value = resp.data
    })
  }
}

// 监听form变化，当获取到订单数据后加载产品信息
watch(() => form.value.productId, (newVal) => {
  if (newVal) {
    getProductData()
  }
})

// 页面加载时获取订单详情
onMounted(() => {
  getOrderDetail()
})
</script>
