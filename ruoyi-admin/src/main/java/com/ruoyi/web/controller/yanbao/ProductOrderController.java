package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.OssUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysFile;
import com.ruoyi.system.service.ISysFileService;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.ProductTerm;
import com.ruoyi.yanbao.entity.Store;
import com.ruoyi.yanbao.entity.vo.ProductOrderVo;
import com.ruoyi.yanbao.service.ProductOrderService;
import com.ruoyi.yanbao.service.ProductService;
import com.ruoyi.yanbao.service.ProductTermService;
import com.ruoyi.yanbao.service.StoreService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/yanbao/order")
public class ProductOrderController extends BaseController {

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private ProductService productService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ProductTermService productTermService;

    @Autowired
    private ISysFileService sysFileService;

    @PreAuthorize("@ss.hasPermi('yanbao:order:add')")
    @Log(title = "新增订单", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody ProductOrderVo order) {
        ProductOrder po = (ProductOrder) order;
        po.setSaleUserId(getUserId());
        po.setSaleUserName(getLoginUser().getUser().getNickName());
        po.setCreatedBy(getLoginUser().getUser().getNickName());
        po.setAuditState(ProductOrderVo.AuditState.WAIT);
        po.setOrderNo(System.currentTimeMillis() + "");
        Product product = productService.getById(order.getProductId());
        if (product == null || !Constants.STATUS_ENABLE.equals(product.getStatus())) {
            return AjaxResult.error("产品已失效");
        }
        po.setProductName(product.getName());
        if (CollectionUtils.isNotEmpty(order.getCertificateImgList())) {
            po.setCertificateImg(order.getCertificateImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getVehicleLicenseImgList())) {
            po.setVehicleLicenseImg(order.getVehicleLicenseImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getCarInvoiceImgList())) {
            po.setCarInvoiceImg(order.getCarInvoiceImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getLeftImgList())) {
            po.setLeftImg(order.getLeftImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getRightImgList())) {
            po.setRightImg(order.getRightImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getLeftbackImgList())) {
            po.setLeftbackImg(order.getLeftbackImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getRightbackImgList())) {
            po.setRightbackImg(order.getRightbackImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getVinImgList())) {
            po.setVinImg(order.getVinImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getCarMileageImgList())) {
            po.setCarMileageImg(order.getCarMileageImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getPurchaseTaxCompleteImgList())) {
            po.setPurchaseTaxCompleteImg(order.getPurchaseTaxCompleteImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getVehicleLicenseInvoiceImgList())) {
            po.setVehicleLicenseInvoiceImg(order.getVehicleLicenseInvoiceImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getTrafficInsuranceImgList())) {
            po.setTrafficInsuranceImg(order.getTrafficInsuranceImgList().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(order.getPayImgList())) {
            po.setPayImg(order.getPayImgList().get(0).getId());
        }
        if (po.getStoreId() != null) {
            Store store = storeService.getById(po.getStoreId());
            po.setStoreName(store.getName());
        }
        if (po.getProductTermId() != null) {
            ProductTerm productTerm = productTermService.getById(po.getProductTermId());
            po.setProductTermName(productTerm.getName());
            po.setServicePeriod(productTerm.getTimeLimit().intValue());
        }
        //提交校验必填项
        if (ProductOrderVo.SubmitState.SUBMITTED.equals(po.getSubmitState())) {

        }
        return toAjax(productOrderService.save(po));
    }

    /**
     * 获取订单详情
     */
    @PreAuthorize("@ss.hasPermi('yanbao:order:detail')")
    @GetMapping("/detail")
    public AjaxResult getOrderDetail(Long orderId) {
        ProductOrder order = productOrderService.getById(orderId);
        if (order == null) {
            return AjaxResult.error("订单不存在");
        }
        ProductOrderVo orderDetail = new ProductOrderVo();
        BeanUtils.copyProperties(order, orderDetail);
        // 设置图片列表
        setImageLists(orderDetail, order);
        return AjaxResult.success(orderDetail);
    }

    /**
     * 设置订单的图片列表
     */
    private void setImageLists(ProductOrderVo orderDetail, ProductOrder order) {
        // 证件图片
        if (order.getCertificateImg() != null) {
            orderDetail.setCertificateImgList(getFileItemList(order.getCertificateImg()));
        }

        // 行驶证图片
        if (order.getVehicleLicenseImg() != null) {
            orderDetail.setVehicleLicenseImgList(getFileItemList(order.getVehicleLicenseImg()));
        }

        // 购车发票图片
        if (order.getCarInvoiceImg() != null) {
            orderDetail.setCarInvoiceImgList(getFileItemList(order.getCarInvoiceImg()));
        }

        // 左前45°图片
        if (order.getLeftImg() != null) {
            orderDetail.setLeftImgList(getFileItemList(order.getLeftImg()));
        }

        // 右前45°图片
        if (order.getRightImg() != null) {
            orderDetail.setRightImgList(getFileItemList(order.getRightImg()));
        }

        // 左后45°图片
        if (order.getLeftbackImg() != null) {
            orderDetail.setLeftbackImgList(getFileItemList(order.getLeftbackImg()));
        }

        // 右后45°图片
        if (order.getRightbackImg() != null) {
            orderDetail.setRightbackImgList(getFileItemList(order.getRightbackImg()));
        }

        // VIN码图片
        if (order.getVinImg() != null) {
            orderDetail.setVinImgList(getFileItemList(order.getVinImg()));
        }

        // 行驶里程图片
        if (order.getCarMileageImg() != null) {
            orderDetail.setCarMileageImgList(getFileItemList(order.getCarMileageImg()));
        }

        // 完税证明图片
        if (order.getPurchaseTaxCompleteImg() != null) {
            orderDetail.setPurchaseTaxCompleteImgList(getFileItemList(order.getPurchaseTaxCompleteImg()));
        }

        // 上牌费用发票图片
        if (order.getVehicleLicenseInvoiceImg() != null) {
            orderDetail.setVehicleLicenseInvoiceImgList(getFileItemList(order.getVehicleLicenseInvoiceImg()));
        }

        // 交强险保单图片
        if (order.getTrafficInsuranceImg() != null) {
            orderDetail.setTrafficInsuranceImgList(getFileItemList(order.getTrafficInsuranceImg()));
        }

        // 付款小票图片
        if (order.getPayImg() != null) {
            orderDetail.setPayImgList(getFileItemList(order.getPayImg()));
        }
    }

    /**
     * 根据文件ID获取文件项列表
     */
    private List<OssUtil.FileItem> getFileItemList(Long fileId) {
        List<OssUtil.FileItem> fileItems = new ArrayList<>();
        if (fileId != null) {
            SysFile sysFile = sysFileService.selectSysFileById(fileId);
            if (sysFile != null) {
                OssUtil.FileItem fileItem = new OssUtil.FileItem();
                fileItem.setId(sysFile.getId());
                fileItem.setName(sysFile.getName());
                fileItem.setPath(sysFile.getPath());
                fileItem.setSize(sysFile.getSize());
                // 根据路径生成访问URL
                fileItem.setUrl(generateFileUrl(sysFile.getPath()));
                fileItems.add(fileItem);
            }
        }
        return fileItems;
    }

    /**
     * 根据文件路径生成访问URL
     */
    private String generateFileUrl(String path) {
        if (StringUtils.isEmpty(path)) {
            return "";
        }
        // 如果是OSS路径，直接返回完整URL
        if (path.startsWith("http://") || path.startsWith("https://")) {
            return path;
        }
        // 否则生成OSS访问URL
        return OssUtil.getObjectUrl(path, null);
    }
}
